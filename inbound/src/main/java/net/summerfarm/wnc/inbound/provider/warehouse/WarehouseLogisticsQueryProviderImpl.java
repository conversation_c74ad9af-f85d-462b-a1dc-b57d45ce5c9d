package net.summerfarm.wnc.inbound.provider.warehouse;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.AddressBelongFenceDTO;
import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.api.fence.service.DeliveryFenceService;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseLogisticsCenterDTO;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WarehouseLogisticsService;
import net.summerfarm.wnc.client.enums.WarehouseLogisticCenterPopFulfillmentWayEnum;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseLogisticsQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseLogisticsQueryReq;
import net.summerfarm.wnc.client.req.warehouse.PopWarehouseLogisticsQueryReq;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;
import net.summerfarm.wnc.common.config.CityAreas;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.config.obj.PopCityAreaOperatingAreaMappingObj;
import net.summerfarm.wnc.common.enums.WarehouseLogisticsCenterEnums;
import net.summerfarm.wnc.common.query.fence.AddressQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsQuery;
import net.summerfarm.wnc.common.util.PopConfigUtil;
import net.summerfarm.wnc.inbound.provider.warehouse.converter.WarehouseLogisticsDTOConverter;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 城配仓查询实现类<br/>
 * date: 2023/10/19 15:10<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class WarehouseLogisticsQueryProviderImpl implements WarehouseLogisticsQueryProvider {

    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private WncConfig wncConfig;
    @Autowired
    private PopConfigUtil popConfigUtil;
    @Autowired
    private DeliveryFenceService deliveryFenceService;

    @Override
    public DubboResponse<List<WarehousLogisticsCenterResp>> queryWarehouseLogisticsList(WarehouseLogisticsQueryReq warehouseLogisticsQueryReq) {
        List<WarehouseLogisticsCenterDTO> dtoList = warehouseLogisticsService.queryWarehouseLogisticsList(WarehouseLogisticsQuery.builder()
                .status(warehouseLogisticsQueryReq.getStatus())
                .storeNo(warehouseLogisticsQueryReq.getStoreNo())
                .storeName(warehouseLogisticsQueryReq.getStoreName())
                .storeNos(warehouseLogisticsQueryReq.getStoreNos())
                .fulfillmentType(warehouseLogisticsQueryReq.getFulfillmentType())
                .build());

        return DubboResponse.getOK(dtoList.stream().map(WarehouseLogisticsDTOConverter::dto2WarehousLogisticsCenterResp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<List<WarehousLogisticsCenterResp>> queryPopWarehouseLogisticsList() {
        List<Integer> popStoreNos = wncConfig.queryPopStoreNos();
        if(CollectionUtils.isEmpty(popStoreNos)){
            return DubboResponse.getOK();
        }
        List<WarehouseLogisticsCenterDTO> dtoList = warehouseLogisticsService.queryWarehouseLogisticsList(WarehouseLogisticsQuery.builder()
                .status(WarehouseLogisticsCenterEnums.Status.VALID.getValue())
                .storeNos(popStoreNos)
                .build());
        if (CollectionUtils.isEmpty(dtoList)) {
            return DubboResponse.getOK();
        }
        List<WarehousLogisticsCenterResp> respList = dtoList.stream().map(WarehouseLogisticsDTOConverter::dto2WarehousLogisticsCenterResp).collect(Collectors.toList());
        respList.forEach(x -> x.setPopFulfillmentWay(WarehouseLogisticCenterPopFulfillmentWayEnum.PROPRIETARY_DELIVERY.getValue()));

        return DubboResponse.getOK(respList);
    }

    @Override
    public DubboResponse<WarehousLogisticsCenterResp> queryPopWarehouseLogistics(@Valid PopWarehouseLogisticsQueryReq popWarehouseLogisticsQuery) {
        if (StringUtils.isEmpty(popWarehouseLogisticsQuery.getPoi())) {
            log.error("\n poi参数为空 WarehouseLogisticsQueryProvider queryPopWarehouseLogistics poi is null\n");
        }
        // 判断城市是否在配置中
        PopCityAreaOperatingAreaMappingObj matchedPopConfig = popConfigUtil.queryMatchedPopConfig(popWarehouseLogisticsQuery.getCity(), popWarehouseLogisticsQuery.getArea());
        if (matchedPopConfig == null) {
            return DubboResponse.getOK();
        }

        Integer storeNo = matchedPopConfig.getStoreNo();
        if (storeNo == null) {
            // 未指定城配仓时根据城市区域匹配城配仓
            FenceQuery fenceQuery = FenceQuery.builder().city(popWarehouseLogisticsQuery.getCity()).area(popWarehouseLogisticsQuery.getArea()).build();
            FenceDTO fenceDTO = deliveryFenceService.queryDeliveryFence(fenceQuery);
            if (fenceDTO != null) {
                storeNo = fenceDTO.getStoreNo();
            }
        }
        if (storeNo == null) {
            log.error("根据配置找不到对应的POP城配仓，config:{}\n", JSON.toJSONString(matchedPopConfig));
            return DubboResponse.getOK();
        }

        List<WarehouseLogisticsCenterDTO> dtoList = warehouseLogisticsService.queryWarehouseLogisticsList(WarehouseLogisticsQuery.builder()
                .status(WarehouseLogisticsCenterEnums.Status.VALID.getValue())
                .storeNos(Lists.newArrayList(storeNo))
                .build());
        if(CollectionUtils.isEmpty(dtoList)){
            return DubboResponse.getOK();
        }
        WarehousLogisticsCenterResp warehousLogisticsCenterResp = WarehouseLogisticsDTOConverter.dto2WarehousLogisticsCenterResp(dtoList.get(0));
        warehousLogisticsCenterResp.setPopFulfillmentWay(matchedPopConfig.getPopFulfillmentWay());
        return DubboResponse.getOK(warehousLogisticsCenterResp);
    }

    @Override
    public DubboResponse<List<WarehousLogisticsCenterResp>> querySharedDeliveryPopWarehouseLogistics() {
        List<PopCityAreaOperatingAreaMappingObj> mappingObjList = wncConfig.queryPopCityAreaOperatingAreaMapping();
        if(CollectionUtils.isEmpty(mappingObjList)){
            return DubboResponse.getOK();
        }
        List<PopCityAreaOperatingAreaMappingObj> sharedDeliveryConfig = mappingObjList.stream()
                .filter(e -> Objects.equals(e.getPopFulfillmentWay(), WarehouseLogisticCenterPopFulfillmentWayEnum.SHARED_DELIVERY.getValue()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(sharedDeliveryConfig)){
            return DubboResponse.getOK();
        }

        List<AddressQuery> addressQueries = sharedDeliveryConfig.stream().map(e -> {
            String city = e.getCity();
            List<String> areas = e.getAreas();
            if (!CollectionUtils.isEmpty(areas)) {
                for (String area : areas) {
                    AddressQuery addressQuery = new AddressQuery();
                    addressQuery.setCity(city);
                    addressQuery.setArea(area);
                    return addressQuery;
                }
            }
            AddressQuery addressQuery = new AddressQuery();
            addressQuery.setCity(city);
            return addressQuery;
        }).collect(Collectors.toList());

        List<AddressBelongFenceDTO> addressBelongFenceDTOS = deliveryFenceService.batchQueryAddressBelongFence(addressQueries);
        if(CollectionUtils.isEmpty(addressBelongFenceDTOS)){
            return DubboResponse.getOK();
        }
        List<Integer> sharedStoreNos = addressBelongFenceDTOS.stream()
                .map(AddressBelongFenceDTO::getFenceDTO)
                .filter(Objects::nonNull)
                .map(FenceDTO::getStoreNo)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        List<WarehouseLogisticsCenterDTO> sharedDeliveryWarehouseLogisticsCenters = warehouseLogisticsService.queryWarehouseLogisticsList(WarehouseLogisticsQuery.builder()
                .status(WarehouseLogisticsCenterEnums.Status.VALID.getValue())
                .storeNos(sharedStoreNos)
                .build());

        List<WarehousLogisticsCenterResp> warehousLogisticsCenterRespList = sharedDeliveryWarehouseLogisticsCenters.stream().map(WarehouseLogisticsDTOConverter::dto2WarehousLogisticsCenterResp).collect(Collectors.toList());
        warehousLogisticsCenterRespList.forEach(e -> e.setPopFulfillmentWay(WarehouseLogisticCenterPopFulfillmentWayEnum.SHARED_DELIVERY.getValue()));

        return DubboResponse.getOK(warehousLogisticsCenterRespList);
    }

    @Override
    public DubboResponse<Boolean> queryIsSupportAddOrder(Integer storeNo) {
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }
        return DubboResponse.getOK(warehouseLogisticsService.queryIsSupportAddOrderByStoreNo(storeNo));
    }

}
