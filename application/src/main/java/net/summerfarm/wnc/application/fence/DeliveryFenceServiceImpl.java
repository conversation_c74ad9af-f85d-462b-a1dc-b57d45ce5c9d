package net.summerfarm.wnc.application.fence;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.goods.client.enums.SubAgentTypeEnum;
import net.summerfarm.wnc.api.fence.dto.*;
import net.summerfarm.wnc.api.fence.dto.area.AreaDTO;
import net.summerfarm.wnc.api.fence.dto.area.SimpleAreaDTO;
import net.summerfarm.wnc.api.fence.service.DeliveryFenceService;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncSkuWarehouseMappingService;
import net.summerfarm.wnc.application.fence.converter.AreaConvert;
import net.summerfarm.wnc.application.fence.converter.ContactAddressBelongFenceConverter;
import net.summerfarm.wnc.application.fence.converter.FenceEntityConverter;
import net.summerfarm.wnc.application.fence.dto.XmPopDeliveryDTO;
import net.summerfarm.wnc.application.fence.handle.DeliveryFenceRuleHandle;
import net.summerfarm.wnc.application.fence.strategy.DeliveryFenceQueryStrategy;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.config.obj.PopCityAreaOperatingAreaMappingObj;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.enums.PopDeliveryDateRuleEnum;
import net.summerfarm.wnc.common.enums.PopFulfillmentWayEnum;
import net.summerfarm.wnc.common.enums.SkuSubTypEnum;
import net.summerfarm.wnc.common.query.fence.AddressQuery;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.fence.NeedOrderTimeQuery;
import net.summerfarm.wnc.common.query.warehouse.StoreNoQuery;
import net.summerfarm.wnc.common.util.PopConfigUtil;
import net.summerfarm.wnc.domain.config.repository.ContactConfigQueryRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.config.repository.WncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
import net.summerfarm.wnc.domain.fence.*;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.fence.param.CloseTimeQueryParam;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.summerfarm.wnc.facade.gc.CrossBizGoodsMappingQueryFacade;
import net.summerfarm.wnc.facade.gc.GcQueryFacade;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDTO;
import net.summerfarm.wnc.facade.saas.SaasTenantQueryFacade;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-06-19
 **/
@Service
@Slf4j
public class DeliveryFenceServiceImpl implements DeliveryFenceService {

	@Autowired
	private DeliveryFenceQueryRegistry deliveryFenceQueryRegistry;
	@Resource
	private DeliveryFenceDomainService deliveryFenceDomainService;
	@Resource
	private AdCodeMsgRepository adCodeMsgRepository;
	@Resource
	private AreaRepository areaRepository;
	@Resource
	private AreaConvert areaConvert;
	@Resource
	private ContactConfigQueryRepository contactConfigQueryRepository;
	@Resource
	private GcQueryFacade gcQueryFacade;
	@Resource
	private DeliveryFenceRepository deliveryFenceRepository;
	@Resource
	private FenceRepository fenceRepository;
	@Resource
	private WncConfig wncConfig;
	@Autowired
	private PopConfigUtil popConfigUtil;
	@Resource
	private DeliveryFenceRuleHandle deliveryFenceRuleHandle;
	@Resource
	private WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
	@Resource
	private WncSkuWarehouseMappingService wncSkuWarehouseMappingService;
	@Resource
	private WncFullCategoryWarehouseSkuWhiteConfigQueryRepository wncFullCategoryWarehouseSkuWhiteConfigQueryRepository;
	@Resource
	private CrossBizGoodsMappingQueryFacade crossBizGoodsMappingQueryFacade;

	@Override
	public DeliveryFenceDTO queryDeliveryDateInfo(DeliveryFenceDateQuery deliveryFenceDateQuery) {
		log.info("rep queryDeliveryDateInfo:{}", JSON.toJSONString(deliveryFenceDateQuery));
		SourceEnum orderSourceEnum = deliveryFenceDateQuery.getOrderSourceEnum();
		DeliveryFenceQueryStrategy worker = deliveryFenceQueryRegistry.getWorker(orderSourceEnum);

		OutLandContactEntity outLandContactEntity = worker.getContact(deliveryFenceDateQuery);
		if (Objects.isNull(outLandContactEntity)) {
			throw new BizException("联系人信息为空");
		}
		List<LocalDate> wantDeliveryTime = deliveryFenceDateQuery.getWantDeliveryTime();
		LocalDateTime orderTime = deliveryFenceDateQuery.getOrderTime();
		//查询围栏相关的信息
		DeliveryFenceEntity deliveryFenceEntity = deliveryFenceDomainService.handleInstallDeliveryFence(outLandContactEntity,deliveryFenceDateQuery);
		//找出首配日期
		LocalDate startTime = deliveryFenceDomainService.findFirstDeliveryDate(deliveryFenceEntity,deliveryFenceDateQuery);
		//计算配送日期（已处理城配仓停配）(非全品类配送日期)
		List<LocalDate> deliveryDateList = deliveryFenceRuleHandle.getDeliveryTimeList(wantDeliveryTime, deliveryFenceEntity, startTime, orderSourceEnum);
		//查询全品类的配送日期(已处理停配)，POP T+2也会使用该日期
		LocalDate fullCategoryDeliveryDay = null;
		// 下一个配送日期
		LocalDate nextDeliveryTime = null;
		List<Long> chageeTenantIdList = wncConfig.queryChageeTenantIdList();
		if (!CollectionUtils.isEmpty(deliveryDateList)) {
			if (chageeTenantIdList.contains(outLandContactEntity.getTenantId())) {
				nextDeliveryTime = deliveryFenceRuleHandle.queryNextDeliveryTime(deliveryFenceEntity, deliveryDateList.get(0), orderSourceEnum);
			} else {
				fullCategoryDeliveryDay = deliveryFenceRuleHandle.queryFullCategoryDeliveryTime(deliveryFenceEntity, deliveryDateList.get(0), orderSourceEnum, orderTime);
			}
		}

		DeliveryFenceDTO deliveryFenceDTO = new DeliveryFenceDTO();
		deliveryFenceDTO.setCloseTime(deliveryFenceEntity.buildOrderTimeCloseTime(orderTime) != null ? deliveryFenceEntity.buildOrderTimeCloseTime(orderTime).toLocalTime() : null);
		deliveryFenceDTO.setIsEveryDayFlag(deliveryFenceEntity.handleEveryDaySendFlag());
		deliveryFenceDTO.setDeliveryDateList(deliveryDateList);
		deliveryFenceDTO.setAddOrderHourDuration(deliveryFenceEntity.getCouldAddOrderTimeHours());
		if(!CollectionUtils.isEmpty(deliveryDateList)){
			deliveryFenceDTO.setDeliveryTime(deliveryDateList.get(0));
			deliveryFenceDTO.setDeliveryCloseTime(deliveryFenceEntity.deliveryCloseTimeJudge(deliveryDateList.get(0)));
		}
		if(chageeTenantIdList.contains(outLandContactEntity.getTenantId())){
			deliveryFenceDTO.setFirstMerchantDeliveryTime(CollectionUtils.isEmpty(deliveryDateList) ? null : deliveryDateList.get(0));
			deliveryFenceDTO.setNextMerchantDeliveryTime(nextDeliveryTime);
		}
		deliveryFenceDTO.setFullCategoryDeliveryDay(fullCategoryDeliveryDay);
		if(fullCategoryDeliveryDay != null){
			deliveryFenceDTO.setFullCategoryDeliveryCloseTime(LocalDateTime.of(fullCategoryDeliveryDay.minusDays(2),
					deliveryFenceEntity.buildOrderTimeCloseTime(orderTime).toLocalTime()));
		}
		WarehouseLogisticsCenterEntity warehouseLogisticsCenterEntity = deliveryFenceEntity.getWarehouseLogisticsCenterEntity();
		if(warehouseLogisticsCenterEntity != null && warehouseLogisticsCenterEntity.getFulfillmentType() != null){
			deliveryFenceDTO.setFulfillmentMethod(warehouseLogisticsCenterEntity.getFulfillmentType().getValue());
			deliveryFenceDTO.setStoreNo(warehouseLogisticsCenterEntity.getStoreNo());
		}
		// POP配送日期规则指定为T+2的区域，配送时效为T+2
		if (SourceEnum.POP_MALL.equals(orderSourceEnum) || SourceEnum.POP_AFTER_SALE.equals(orderSourceEnum)) {
			// 处理POP配送日期
			List<PopSkuDeliveryDateDTO> popSkuDeliveryDates = handlerPopSkuDeliveryDateDTOS(deliveryFenceDateQuery, deliveryFenceEntity, deliveryFenceDTO,outLandContactEntity);
			deliveryFenceDTO.setPopSkuDeliveryDates(popSkuDeliveryDates);
		}
		return deliveryFenceDTO;
	}

	/**
	 * 处理POP配送日期
	 *
	 * @param deliveryFenceDateQuery 查询
	 * @param deliveryFenceEntity    围栏实体
	 * @param deliveryFenceDTO       围栏配送DTO
	 * @param outLandContactEntity	 联系人信息
	 * @return POP sku的配送日期信息
	 */
	private List<PopSkuDeliveryDateDTO> handlerPopSkuDeliveryDateDTOS(DeliveryFenceDateQuery deliveryFenceDateQuery,
																	  DeliveryFenceEntity deliveryFenceEntity,
																	  DeliveryFenceDTO deliveryFenceDTO,
																	  OutLandContactEntity outLandContactEntity) {
		String city = outLandContactEntity.getCity();
		String area = outLandContactEntity.getArea();

		// POP围栏查询
		PopCityAreaOperatingAreaMappingObj matchedPopConfig = null;
		// 白名单放行
		if (deliveryFenceDateQuery.getMerchantId() != null && wncConfig.getPopT1MerchantWhiteList().contains(deliveryFenceDateQuery.getMerchantId())){
			matchedPopConfig = new PopCityAreaOperatingAreaMappingObj();
			matchedPopConfig.setPopDeliveryDateRule(PopDeliveryDateRuleEnum.T_1.getValue());
		}else{
			// POP围栏查询
			matchedPopConfig = popConfigUtil.queryMatchedPopConfig(city, area);
			if (matchedPopConfig == null) {
				throw new BizException("找不到对应的pop配置, city:" + city + ", area:" + area);
			}
		}

		LocalDateTime orderTime = deliveryFenceDateQuery.getOrderTime();

		List<String> skus = deliveryFenceDateQuery.getSkus();
		if(CollectionUtils.isEmpty(skus)){
			return Collections.emptyList();
		}
		// 1.查询POP商品鲜沐映射POP品
		Map<String, String> pop2xmSkuMap = crossBizGoodsMappingQueryFacade.queryPopToXianMuMapping(skus);

		XmPopDeliveryDTO xmPopDeliveryDTO = new XmPopDeliveryDTO();
		// 2.如果存在鲜沐映射POP品，截单日期不一样，需要重新计算配送日期
		if(!CollectionUtils.isEmpty(pop2xmSkuMap)){
			xmPopDeliveryDTO = this.queryPopMappingXmDeliveryTime(deliveryFenceDateQuery, deliveryFenceEntity,outLandContactEntity);
		}
		List<PopSkuDeliveryDateDTO> popSkuDeliveryDates = new ArrayList<>();
		LocalDate fullCategoryDeliveryDay = deliveryFenceDTO.getFullCategoryDeliveryDay();
		for (String sku : skus) {
			PopSkuDeliveryDateDTO popSkuDeliveryDateDTO = new PopSkuDeliveryDateDTO();
			popSkuDeliveryDateDTO.setSku(sku);

			// 鲜沐映射POP品逻辑
			if(pop2xmSkuMap.get(sku) != null){
				// 鲜沐映射POP品
				popSkuDeliveryDateDTO.setDeliveryDate(xmPopDeliveryDTO.getDeliveryTime());
				popSkuDeliveryDateDTO.setDeliveryCloseTime(xmPopDeliveryDTO.getDeliveryCloseTime());
				popSkuDeliveryDateDTO.setIsXmPopSku(true);

				popSkuDeliveryDates.add(popSkuDeliveryDateDTO);
				// 结束
				continue;
			}

			// 纯POP品
			if (PopDeliveryDateRuleEnum.T_2.getValue().equals(matchedPopConfig.getPopDeliveryDateRule()) && fullCategoryDeliveryDay != null) {
				popSkuDeliveryDateDTO.setDeliveryCloseTime(LocalDateTime.of(fullCategoryDeliveryDay.minusDays(2), deliveryFenceEntity.buildOrderTimeCloseTime(orderTime).toLocalTime()));
				popSkuDeliveryDateDTO.setDeliveryDate(fullCategoryDeliveryDay);
				popSkuDeliveryDateDTO.setIsXmPopSku(false);
			} else {
				popSkuDeliveryDateDTO.setDeliveryCloseTime(deliveryFenceEntity.deliveryCloseTimeJudge(deliveryFenceDTO.getDeliveryTime()));
				popSkuDeliveryDateDTO.setDeliveryDate(deliveryFenceDTO.getDeliveryTime());
				popSkuDeliveryDateDTO.setIsXmPopSku(false);
			}

			popSkuDeliveryDates.add(popSkuDeliveryDateDTO);
		}
		return popSkuDeliveryDates;
	}

	/**
	 * 处理xm POP配送日期
	 *
	 * @param deliveryFenceDateQuery 查询
	 * @param deliveryFenceEntityReq 围栏实体
	 * @param outLandContactEntity   客户信息
	 * @return POP sku的配送日期信息
	 */
	private XmPopDeliveryDTO queryPopMappingXmDeliveryTime(DeliveryFenceDateQuery deliveryFenceDateQuery,
														   DeliveryFenceEntity deliveryFenceEntityReq,
														   OutLandContactEntity outLandContactEntity) {
		if(deliveryFenceEntityReq == null){
			return null;
		}
		// 深复制deliveryFenceEntity
		DeliveryFenceEntity deliveryFenceEntity = JSON.parseObject(JSON.toJSONString(deliveryFenceEntityReq), DeliveryFenceEntity.class);

		SourceEnum orderSourceEnum = deliveryFenceDateQuery.getOrderSourceEnum();
		String city = outLandContactEntity.getCity();
		String area = outLandContactEntity.getArea();
		Long tenantId = deliveryFenceDateQuery.getTenantId();
		Long contactId = deliveryFenceDateQuery.getContactId();
		Long merchantId = deliveryFenceDateQuery.getMerchantId();

		// 查询鲜沐品的截单时间
		LocalTime xmStoreNoCloseTime = deliveryFenceDomainService.queryCloseTime(CloseTimeQueryParam.builder()
				.city(city)
				.area(area)
				.source(deliveryFenceDateQuery.getOrderSourceEnum())
				.contactId(contactId)
				.tenantId(tenantId)
				.merchantId(merchantId)
				.isXmPopSkuType(true)
				.build());
		deliveryFenceEntity.setCloseTime(xmStoreNoCloseTime);
		LocalDate popStartTime = deliveryFenceDomainService.findFirstDeliveryDate(deliveryFenceEntity,deliveryFenceDateQuery);
		List<LocalDate> xmPopDeliveryDateList = deliveryFenceRuleHandle.getDeliveryTimeList(null, deliveryFenceEntity, popStartTime, orderSourceEnum);

		XmPopDeliveryDTO xmPopDeliveryDTO = new XmPopDeliveryDTO();
		if (!CollectionUtils.isEmpty(xmPopDeliveryDateList)) {
			LocalDate deliveryTime = xmPopDeliveryDateList.get(0);

			xmPopDeliveryDTO.setDeliveryTime(deliveryTime);
			xmPopDeliveryDTO.setDeliveryCloseTime(deliveryFenceEntity.deliveryCloseTimeJudge(deliveryTime));
		}

		return xmPopDeliveryDTO;
	}


	@Override
	public LocalTime queryCloseTime(DeliveryFenceCloseTimeQuery deliveryFenceCloseTimeQueryDTO) {
		// 根据SKU查询PMS
		Map<String, String> pop2xmSkuMap = crossBizGoodsMappingQueryFacade.queryPopToXianMuMapping(Collections.singletonList(deliveryFenceCloseTimeQueryDTO.getSku()));
		return deliveryFenceDomainService.queryCloseTime(CloseTimeQueryParam.builder()
				.source(deliveryFenceCloseTimeQueryDTO.getSource())
				.contactId(deliveryFenceCloseTimeQueryDTO.getContactId())
				.city(deliveryFenceCloseTimeQueryDTO.getCity())
				.area(deliveryFenceCloseTimeQueryDTO.getArea())
				.tenantId(deliveryFenceCloseTimeQueryDTO.getTenantId())
				.isXmPopSkuType(pop2xmSkuMap.get(deliveryFenceCloseTimeQueryDTO.getSku()) != null)
				.build());
	}


	@Override
	public List<SimpleAreaDTO> queryAreaLegitimacy(List<String> cityList) {
		if (StringUtils.isBlank(cityList)) {
			throw new BizException("请求参数不能为空");
		}
		List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgRepository.queryByCityList(cityList);
		if (CollectionUtils.isEmpty(adCodeMsgEntities)) {
			return null;
		}
		List<SimpleAreaDTO> respList = Lists.newArrayList();
		for (AdCodeMsgEntity adCodeMsgEntity : adCodeMsgEntities) {
			SimpleAreaDTO simpleAreaDTO = new SimpleAreaDTO();
			simpleAreaDTO.setCity(adCodeMsgEntity.getCity());
			simpleAreaDTO.setProvince(adCodeMsgEntity.getProvince());
			respList.add(simpleAreaDTO);
		}
		return respList;
	}


	@Override
	public AreaDTO queryAreaByAddress(@NonNull String city, String area) {
		AreaEntity areaEntity = areaRepository.queryByAddress(city, area);
		return areaConvert.entityToDTO(areaEntity);

	}

	@Override
	public FenceDTO queryStoreNo(StoreNoQuery storeNoQuery) {
		if(storeNoQuery.getTenantId() == null && storeNoQuery.getContactId() != null){
			log.info("查询客户指定城配仓方法queryStoreNo租户ID为空,联系人Id不为空，请注意，需要看下上游场景",
					new BizException("查询客户指定城配仓方法queryStoreNo租户ID为空,联系人Id不为空，请注意，需要看下上游场景"));
		}
		if(storeNoQuery.getTenantId() != null && storeNoQuery.getContactId() == null){
			log.info("查询客户指定城配仓方法queryStoreNo租户ID不为空,联系人Id为空，请注意，需要看下上游场景",
					new BizException("查询客户指定城配仓方法queryStoreNo租户ID为空,联系人Id不为空，请注意，需要看下上游场景"));
		}
		FenceDTO fenceDTO = new FenceDTO();
		if (storeNoQuery.getContactId() != null && storeNoQuery.getTenantId() != null){
			ContactConfigEntity contactConfigEntity = contactConfigQueryRepository.queryByUk(ContactConfigEnums.Source.getSourceByTenantId(storeNoQuery.getTenantId()), storeNoQuery.getContactId());
			if (contactConfigEntity != null){
				log.info("查询指定城配仓，门店ID：{}，绑定城配仓编号：{}", storeNoQuery.getContactId(), contactConfigEntity.getStoreNo());
				fenceDTO.setStoreNo(contactConfigEntity.getStoreNo());
				fenceDTO.setIsStoreNoAppointment(true);
			}
		}
		if(fenceDTO.getStoreNo() == null){
			FenceQuery fenceQuery = FenceQuery.builder()
					.city(storeNoQuery.getCity())
					.area(storeNoQuery.getArea())
					.poi(storeNoQuery.getPoi()).build();
			fenceDTO = this.queryDeliveryFence(fenceQuery);
			if(fenceDTO == null){
				return null;
			}
			fenceDTO.setIsStoreNoAppointment(false);
		}

		if(fenceDTO.getStoreNo() != null){
			WarehouseLogisticsCenterEntity storeNoEntity = warehouseLogisticsCenterRepository.queryByUk(fenceDTO.getStoreNo());
			if(storeNoEntity == null){
				return fenceDTO;
			}
			fenceDTO.setStoreName(storeNoEntity.getStoreName());
			fenceDTO.setFulfillmentMethod(storeNoEntity.getFulfillmentType() != null? storeNoEntity.getFulfillmentType().getValue() : null);
		}
		return fenceDTO;
	}

	@Override
	public List<FenceDTO> batchQueryDeliveryFenceByCity(List<String> cityList) {
		if (CollectionUtils.isEmpty(cityList)){
			return Collections.emptyList();
		}
		List<FenceEntity> fenceEntities = deliveryFenceRepository.queryFenceByCityList(cityList);
		return fenceEntities.stream().map(FenceEntityConverter::entity2dto).collect(Collectors.toList());
	}

	@Override
	public List<FenceDTO> queryFenceListWithArea(FenceQuery fenceQuery) {
		List<FenceEntity> fenceEntities = fenceRepository.queryListWithArea(fenceQuery);
		if (CollectionUtils.isEmpty(fenceEntities)){
			return Collections.emptyList();
		}
		return fenceEntities.stream().map(FenceEntityConverter::entity2dto).collect(Collectors.toList());
	}

	@Override
	public List<AddressBelongFenceDTO> batchQueryAddressBelongFence(List<AddressQuery> addressQueryList) {
		if (CollectionUtils.isEmpty(addressQueryList)){
			return Collections.emptyList();
		}
		List<AddressBelongFenceEntity> addressBelongFenceEntities = deliveryFenceRepository.queryAddressBelongFence(addressQueryList);
		if (CollectionUtils.isEmpty(addressBelongFenceEntities)){
			return Collections.emptyList();
		}
		return addressBelongFenceEntities.stream().map(ContactAddressBelongFenceConverter::entity2dto).collect(Collectors.toList());
	}

	@Override
	public FenceDTO queryDeliveryFence(FenceQuery fenceQuery) {
		FenceEntity fenceEntity = deliveryFenceDomainService.queryDeliveryFence(fenceQuery);
		return FenceEntityConverter.entity2dto(fenceEntity);
	}

	@Override
	public List<SkuDeliveryDateDTO> handleSkuDeliveryInfo(List<String> skus, DeliveryFenceDTO deliveryFenceDTO) {
		//查询sku商品信息
		List<SkuDeliveryDateDTO> skuDeliveryDateList = new ArrayList<>();
		if(deliveryFenceDTO == null || CollectionUtils.isEmpty(skus)){
			return skuDeliveryDateList;
		}
		List<ProductSkuDTO> productSkus = gcQueryFacade.querySkuListInfo(skus.stream().distinct().collect(Collectors.toList()));
		if(CollectionUtils.isEmpty(productSkus)){
			return skuDeliveryDateList;
		}
		List<Integer> selfWarehouseNos = wncConfig.querySelfWarehouseNos();
		//代销不入仓品
		List<ProductSkuDTO> selfSaleNoWarehouseSkuDTOList = productSkus.stream().filter(productSku -> Objects.equals(SkuSubTypEnum.SELF_SALE_NO_WAREHOUSE.getCode(), productSku.getSubType())).collect(Collectors.toList());
		//key -> sku+"#"+storeNo
		//value -> warehouseNo
		Map<String, Integer> sellingWithoutWarehouseUk2MappingMap = new HashMap<>();
		if(!CollectionUtils.isEmpty(selfSaleNoWarehouseSkuDTOList)){
			List<String> selfSaleNoWarehouseSkuList = selfSaleNoWarehouseSkuDTOList.stream().map(ProductSkuDTO::getSku).collect(Collectors.toList());
			sellingWithoutWarehouseUk2MappingMap = wncSkuWarehouseMappingService.queryMapping2SkuStoreNoMap(selfSaleNoWarehouseSkuList,deliveryFenceDTO.getStoreNo());
		}
		//全品类
		LocalDate fullCategoryDeliveryDay = deliveryFenceDTO.getFullCategoryDeliveryDay();
		LocalDateTime fullCategoryDeliveryCloseTime = deliveryFenceDTO.getFullCategoryDeliveryCloseTime();
		//非全品类
		LocalDate deliveryTime = deliveryFenceDTO.getDeliveryTime();
		LocalDateTime deliveryCloseTime = deliveryFenceDTO.getDeliveryCloseTime();

		//按照全品和非全品分组
		Map<String, ProductSkuDTO> skuMap = productSkus.stream().collect(Collectors.toMap(ProductSkuDTO::getSku, Function.identity(), (oldData, newData) -> newData));

		// 代销不入仓T+2配送仓库白名单
		Map<String, List<Integer>> fullCategoryWarehouseSkuWhiteConfigMap = wncFullCategoryWarehouseSkuWhiteConfigQueryRepository.selectWhiteConfigMapBySkusWarehouseNos(skus,
				new ArrayList<>(sellingWithoutWarehouseUk2MappingMap.values()));

		// 非自营仓-代销不入仓-T+2配送的仓库编号集合
		List<Integer> nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2WarehouseNos = wncConfig.queryNonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2();

		List<PopSkuDeliveryDateDTO> popSkuDeliveryDates = deliveryFenceDTO.getPopSkuDeliveryDates();
		Map<String, PopSkuDeliveryDateDTO> popSku2DeliveryDateMap = new HashMap<>();
		if(!CollectionUtils.isEmpty(popSkuDeliveryDates)){
			popSku2DeliveryDateMap = popSkuDeliveryDates
					.stream()
					.filter(popSkuDeliveryDateDTO -> popSkuDeliveryDateDTO.getSku() != null)
					.collect(Collectors.toMap(PopSkuDeliveryDateDTO::getSku, Function.identity(), (oldData, newData) -> newData));
		}

		for (String sku : skuMap.keySet()) {
			ProductSkuDTO productSkuDTO = skuMap.get(sku);

			SkuDeliveryDateDTO skuDeliveryDateDTO = new SkuDeliveryDateDTO();
			skuDeliveryDateDTO.setSku(sku);
			skuDeliveryDateDTO.setSubType(productSkuDTO.getSubType());
			//是否是自营仓 true是 false不是
			boolean isSelfWarehouseNoFlag = true;
			if(!CollectionUtils.isEmpty(selfWarehouseNos)){
				isSelfWarehouseNoFlag = selfWarehouseNos.contains(sellingWithoutWarehouseUk2MappingMap.get(sku + "#" + deliveryFenceDTO.getStoreNo()));
			}

			//代销不入仓 非自营仓SKU的白名单 true是 false不是
			boolean selfSaleNoWarehouseWarehouseWhiteListFlag = false;
			if(!CollectionUtils.isEmpty(fullCategoryWarehouseSkuWhiteConfigMap)){
				Integer mappingWarehouseNo = sellingWithoutWarehouseUk2MappingMap.get(sku + "#" + deliveryFenceDTO.getStoreNo());
				List<Integer> whiteWarehouseNoList = fullCategoryWarehouseSkuWhiteConfigMap.getOrDefault(sku, Collections.emptyList());
				selfSaleNoWarehouseWarehouseWhiteListFlag = whiteWarehouseNoList.contains(mappingWarehouseNo);
			}

			// 非自营仓-代销不入仓-T+2标识
			boolean nonSelfWarehouseNoSaleWithoutWarehouseT2Flag = false;
			if(!CollectionUtils.isEmpty(nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2WarehouseNos)){
				Integer mappingWarehouseNo = sellingWithoutWarehouseUk2MappingMap.get(sku + "#" + deliveryFenceDTO.getStoreNo());
				nonSelfWarehouseNoSaleWithoutWarehouseT2Flag = nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2WarehouseNos.contains(mappingWarehouseNo);
			}

			log.info("自营仓标识:{},代销不入仓非自营仓SKU白名单标识:{},非自营仓-代销不入仓-T+2标识:{}", isSelfWarehouseNoFlag, selfSaleNoWarehouseWarehouseWhiteListFlag, nonSelfWarehouseNoSaleWithoutWarehouseT2Flag);
			//全品类配送日期判断
			if(Objects.equals(SkuSubTypEnum.SELF_SALE_NO_WAREHOUSE.getCode(),productSkuDTO.getSubType())){
				if(isSelfWarehouseNoFlag){
					// 自营仓走全品类日期
					skuDeliveryDateDTO.setDeliveryDate(fullCategoryDeliveryDay);
					skuDeliveryDateDTO.setDeliveryCloseTime(fullCategoryDeliveryCloseTime);
				}else if(selfSaleNoWarehouseWarehouseWhiteListFlag){
					// 代销不入仓SKU+仓库白名单走全品类日期
					skuDeliveryDateDTO.setDeliveryDate(fullCategoryDeliveryDay);
					skuDeliveryDateDTO.setDeliveryCloseTime(fullCategoryDeliveryCloseTime);
				}else if(nonSelfWarehouseNoSaleWithoutWarehouseT2Flag){
					// 非自营仓-代销不入仓-T+2标识
					skuDeliveryDateDTO.setDeliveryDate(fullCategoryDeliveryDay);
					skuDeliveryDateDTO.setDeliveryCloseTime(fullCategoryDeliveryCloseTime);
				}else{
					//全品类 非自营仓 走T+1
					skuDeliveryDateDTO.setDeliveryDate(deliveryTime);
					skuDeliveryDateDTO.setDeliveryCloseTime(deliveryCloseTime);
				}
			} else if(Objects.equals(SkuSubTypEnum.POP.getCode(),productSkuDTO.getSubType())){
				PopSkuDeliveryDateDTO popSkuDeliveryDateDTO = popSku2DeliveryDateMap.get(sku);
				if(popSkuDeliveryDateDTO != null){
					skuDeliveryDateDTO.setDeliveryDate(popSkuDeliveryDateDTO.getDeliveryDate());
					skuDeliveryDateDTO.setDeliveryCloseTime(popSkuDeliveryDateDTO.getDeliveryCloseTime());
				}
			} else {
				//非全品
				skuDeliveryDateDTO.setDeliveryDate(deliveryTime);
				skuDeliveryDateDTO.setDeliveryCloseTime(deliveryCloseTime);
			}
			skuDeliveryDateList.add(skuDeliveryDateDTO);
		}
		return skuDeliveryDateList;
	}

	@Override
	public DeliveryFenceDTO querySkuDeliveryDateInfo(DeliveryFenceDateQuery query) {
		//查询围栏配送信息
		DeliveryFenceDTO deliveryFenceDTO = this.queryDeliveryDateInfo(query);
		//获取sku配送信息
		List<SkuDeliveryDateDTO> skuDeliveryDateDTOList = this.handleSkuDeliveryInfo(query.getSkus(), deliveryFenceDTO);
		if(deliveryFenceDTO != null){
			deliveryFenceDTO.setSkuDeliveryDates(skuDeliveryDateDTOList);
		}
		return deliveryFenceDTO;
	}

	/**
	 * 处理全品类范围查询
	 * @param wantDeliveryTimeLists 查询想配送日期集合
	 * @param deliveryFenceDTO 配送信息
	 */
	@Override
	public void handleWantDeliveryDate(List<LocalDate> wantDeliveryTimeLists, DeliveryFenceDTO deliveryFenceDTO) {
		if(CollectionUtils.isEmpty(wantDeliveryTimeLists) || deliveryFenceDTO == null){
			return;
		}
		List<SkuDeliveryDateDTO> skuDeliveryDates = deliveryFenceDTO.getSkuDeliveryDates();
		if(CollectionUtils.isEmpty(skuDeliveryDates)){
			return;
		}
		LocalDate lastDeliveryDate = skuDeliveryDates.stream()
				.map(SkuDeliveryDateDTO::getDeliveryDate)
				.filter(Objects::nonNull)
				.max(Comparator.naturalOrder())
				.orElse(null);

		if(lastDeliveryDate == null){
			return;
		}

		wantDeliveryTimeLists = wantDeliveryTimeLists.stream()
				.filter(time -> time.compareTo(lastDeliveryDate) >= 0)
				.collect(Collectors.toList());
		//取最大
		if(CollectionUtils.isEmpty(wantDeliveryTimeLists)){
			deliveryFenceDTO.setDeliveryDateList(Collections.singletonList(lastDeliveryDate));
		}else{
			deliveryFenceDTO.setDeliveryDateList(wantDeliveryTimeLists);
		}
	}

	@Override
	public List<String> findNeedOrderTimeQueryDeliveryDateSkuList(NeedOrderTimeQuery query) {
		List<String> skus = query.getSkus();
		// 查询客户归属城配仓
		FenceDTO fenceDTO = this.queryStoreNo(StoreNoQuery.builder()
				.city(query.getCity())
				.area(query.getArea())
				.contactId(query.getContactId())
				.tenantId(query.getTenantId()).build());
		if(fenceDTO == null){
			throw new RuntimeException("未查询到客户归属城配仓");
		}
		Integer storeNo = fenceDTO.getStoreNo();
		log.info("客户归属城配仓信息:{},城配仓编号:{}", JSON.toJSONString(fenceDTO),storeNo);

		// 查询SKU的属性信息
		List<ProductSkuDTO> productSkus = gcQueryFacade.querySkuListInfo(skus.stream().distinct().collect(Collectors.toList()));
		log.info("SKU属性信息:{}", JSON.toJSONString(productSkus));
		if(CollectionUtils.isEmpty(productSkus)){
			throw new RuntimeException("未查询到SKU属性信息");
		}

		// 查询仓库映射关系 key -> sku+"#"+storeNo value -> warehouseNo
		Map<String, Integer> uk2MappingMap =  wncSkuWarehouseMappingService.queryMapping2SkuStoreNoMap(skus,storeNo);
		log.info("仓库映射关系:{}", JSON.toJSONString(uk2MappingMap));
		// 过滤代销不入仓、POP品
		List<Integer> selfSaleNoWarePopSkuListSubTypes = Arrays.asList(SubAgentTypeEnum.CONSIGNMENT_NOT_WAREHOUSING.getValue(), SubAgentTypeEnum.POP.getValue());
		List<ProductSkuDTO> selfSaleNoWarePopSkuList = productSkus.stream().filter(skuBaseInfo -> selfSaleNoWarePopSkuListSubTypes.contains(skuBaseInfo.getSubType())).collect(Collectors.toList());

		// 自营仓配置
		List<Integer> selfWarehouseNos = wncConfig.querySelfWarehouseNos();
		log.info("自营仓配置:{}", JSON.toJSONString(selfWarehouseNos));

		// 代销不入仓T+2配送仓库白名单
		List<Integer> warehouseNos = new ArrayList<>(uk2MappingMap.values());
		Map<String, List<Integer>> fullCategoryWarehouseSkuWhiteConfigMap = wncFullCategoryWarehouseSkuWhiteConfigQueryRepository.selectWhiteConfigMapBySkusWarehouseNos(skus, warehouseNos);
		log.info("代销不入仓T+2配送仓库白名单:{}", JSON.toJSONString(fullCategoryWarehouseSkuWhiteConfigMap));

		// 非自营仓-代销不入仓-T+2配送的仓库编号集合
		List<Integer> nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2WarehouseNos = wncConfig.queryNonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2();
		log.info("非自营仓-代销不入仓-T+2配送的仓库编号集合:{}", JSON.toJSONString(nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2WarehouseNos));

		// 代销不入仓SKU（自营仓）、POP品、销不入仓切仓库是白名单的
		List<String> ourWareSelfNoWareAndPopSkuList = selfSaleNoWarePopSkuList.stream().filter(selfSaleNoWarePopSku -> {
			// POP品
			if (Objects.equals(selfSaleNoWarePopSku.getSubType(), SubAgentTypeEnum.POP.getValue())) {
				return true;
			}

			Integer mappingWarehouseNo = uk2MappingMap.get(selfSaleNoWarePopSku.getSku() + "#" + storeNo);

			//是否是自营仓 true是 false不是
			if (!CollectionUtils.isEmpty(selfWarehouseNos)) {
				boolean isSelfWarehouseNoFlag = selfWarehouseNos.contains(mappingWarehouseNo);
				if(isSelfWarehouseNoFlag){
					return true;
				}
			}

			// 是否代销不入仓切仓库和SKU是白名单
			if (!CollectionUtils.isEmpty(fullCategoryWarehouseSkuWhiteConfigMap)) {
				List<Integer> whiteWarehouseNos = fullCategoryWarehouseSkuWhiteConfigMap.getOrDefault(selfSaleNoWarePopSku.getSku(), Collections.emptyList());
				if(whiteWarehouseNos.contains(mappingWarehouseNo)){
					return true;
				}
			}

			if (!CollectionUtils.isEmpty(nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2WarehouseNos)) {
				boolean isNonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2Flag = nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2WarehouseNos.contains(mappingWarehouseNo);
				if(isNonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2Flag){
					return true;
				}
			}

			return false;
		}).map(ProductSkuDTO::getSku).collect(Collectors.toList());

		log.info("代销不入仓SKU（自营仓）、POP品:{}", JSON.toJSONString(ourWareSelfNoWareAndPopSkuList));
		return ourWareSelfNoWareAndPopSkuList;
	}

	@Override
	public List<FenceDTO> batchQueryCityAreaBelongFenceList(List<AddressQuery> addressQueryList) {
		if (CollectionUtils.isEmpty(addressQueryList)){
			return Collections.emptyList();
		}
		List<FenceEntity> fenceEntityList =  deliveryFenceRepository.queryBatchAddressBelongFences(addressQueryList);
		List<FenceEntity> fenceEntityList = deliveryFenceRepository.queryAddressBelongFence(addressQueryList);

		return fenceEntityList.stream().map(FenceEntityConverter::entity2dto);
	}
}
