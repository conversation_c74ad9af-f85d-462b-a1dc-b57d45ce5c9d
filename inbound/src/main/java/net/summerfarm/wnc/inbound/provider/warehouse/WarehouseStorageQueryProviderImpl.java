package net.summerfarm.wnc.inbound.provider.warehouse;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseBaseDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageTenantDTO;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WarehouseStorageCenterService;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WncWarehouseStorageTenantService;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.*;
import net.summerfarm.wnc.client.req.warehouse.WarehouseQueryReq;
import net.summerfarm.wnc.client.resp.*;
import net.summerfarm.wnc.client.resp.warehouse.WarehouseStorageCenterBaseInfoResp;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.common.query.warehouse.WncWarehouseStorageTenantQuery;
import net.summerfarm.wnc.inbound.provider.warehouse.converter.WarehouseSkuAreaDTOConverter;
import net.summerfarm.wnc.inbound.provider.warehouse.converter.WarehouseStorageDTOConverter;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/4 14:50<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class WarehouseStorageQueryProviderImpl implements WarehouseStorageQueryProvider {

    @Resource
    private WarehouseStorageCenterService warehouseStorageCenterService;
    @Resource
    private WncWarehouseStorageTenantService wncWarehouseStorageTenantService;
    @Resource
    private WarehouseSkuAreaDTOConverter warehouseSkuAreaDTOConverter;
    @Autowired
    private WncConfig wncConfig;

    @Override
    public DubboResponse<WarehouseStorageResp> queryOneWarehouseStorage(WarehouseStorageQueryReq warehouseStorageQueryReq) {
        log.info("单个查询仓库信息:{}", JSON.toJSONString(warehouseStorageQueryReq));
        WncAssert.notNull(warehouseStorageQueryReq.getWarehouseNo(), "warehouseNo不能为空");
        WarehouseStorageDTO warehouseStorageDTO = warehouseStorageCenterService.queryByWarehouseNo(warehouseStorageQueryReq.getWarehouseNo());
        return DubboResponse.getOK(WarehouseStorageDTOConverter.warehouseStorageDTO2Resp(warehouseStorageDTO));
    }

    @Override
    public DubboResponse<List<WarehouseStorageResp>> queryWarehouseStorageList(WarehouseStorageListQueryReq warehouseStorageListQueryReq) {
        log.info("批量查询仓库信息:{}", JSON.toJSONString(warehouseStorageListQueryReq));
        if(warehouseStorageListQueryReq.getTenantId() == null && CollectionUtils.isEmpty(warehouseStorageListQueryReq.getWarehouseNos())){
            throw new BizException("租户ID和仓库编号必须要传其中一个");
        }

        List<WarehouseStorageDTO> warehouseStorageDTOS = new ArrayList<>();

        List<WarehouseStorageDTO> warehouseStorageDTOList = warehouseStorageCenterService.queryList(WarehouseStorageQuery.builder()
                .tenantId(warehouseStorageListQueryReq.getTenantId())
                .warehouseNos(warehouseStorageListQueryReq.getWarehouseNos())
                .build());

        List<WncWarehouseStorageTenantDTO> wncWarehouseStorageTenantDTOS = wncWarehouseStorageTenantService.queryTenantWarehouseList(WncWarehouseStorageTenantQuery.builder()
                .tenantId(warehouseStorageListQueryReq.getTenantId())
                .build());

        if(Objects.equals(warehouseStorageListQueryReq.getWarehouseSource() ,WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode())){
            //代仓仓库
            List<WarehouseStorageDTO> summerFarmWarehouseList = warehouseStorageDTOList.stream().filter(warehouseStorageDTO -> Objects.equals(warehouseStorageDTO.getTenantId().toString(), WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode().toString())).collect(Collectors.toList());
            warehouseStorageDTOS.addAll(summerFarmWarehouseList);

            if(!CollectionUtils.isEmpty(wncWarehouseStorageTenantDTOS)){
                List<Long> warehouseNos = wncWarehouseStorageTenantDTOS.stream().map(WncWarehouseStorageTenantDTO::getWarehouseNo).collect(Collectors.toList());
                List<WarehouseStorageDTO> warehouseStorageProxyDTOList = warehouseStorageCenterService.queryList(WarehouseStorageQuery.builder()
                        .warehouseNos(JSON.parseArray(warehouseNos.toString(), Integer.class))
                        .build());
                warehouseStorageDTOS.addAll(warehouseStorageProxyDTOList);
            }
        }else if(Objects.equals(warehouseStorageListQueryReq.getWarehouseSource() ,WarehouseSourceEnum.SAAS_WAREHOUSE.getCode())){
            List<WarehouseStorageDTO> summerFarmWarehouseList = warehouseStorageDTOList.stream().filter(warehouseStorageDTO -> !Objects.equals(warehouseStorageDTO.getTenantId().toString(), WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode().toString())).collect(Collectors.toList());
            warehouseStorageDTOS.addAll(summerFarmWarehouseList);
        }else{
            List<Long> warehouseNos = wncWarehouseStorageTenantDTOS.stream().map(WncWarehouseStorageTenantDTO::getWarehouseNo).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(warehouseNos)){
                List<WarehouseStorageDTO> warehouseStorageProxyDTOList = warehouseStorageCenterService.queryList(WarehouseStorageQuery.builder()
                        .warehouseNos(JSON.parseArray(warehouseNos.toString(), Integer.class))
                        .build());
                for (WarehouseStorageDTO warehouseStorageDTO : warehouseStorageProxyDTOList) {
                    warehouseStorageDTO.setWarehouseServiceName(AppConsts.WarehouseServiceName.xianMu);
                }
                warehouseStorageDTOS.addAll(warehouseStorageProxyDTOList);
            }
            warehouseStorageDTOS.addAll(warehouseStorageDTOList);
        }

        return DubboResponse.getOK(warehouseStorageDTOS.stream().map(WarehouseStorageDTOConverter::warehouseStorageDTO2Resp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<List<WarehouseSkuResp>> queryXmWarehouseBySkuCity(WarehouseBySkuCityQueryReq warehouseBySkuCityQueryReq) {
        log.info("queryWarehouseBySkuCity根据sku和城市信息请求报文:{}",JSON.toJSONString(warehouseBySkuCityQueryReq));
        WncAssert.notEmpty(warehouseBySkuCityQueryReq.getCity(),"city不能为空");
        WncAssert.notEmpty(warehouseBySkuCityQueryReq.getSkuList(),"skuList不能为空");

        if(warehouseBySkuCityQueryReq.getSkuList().size() > 100){
            DubboResponse.getDefaultError("查询超过最大数量100");
        }
        //根据城市和sku集合查询仓库
        List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageCenterService.queryXmWarehouseBySkuCity(WarehouseStorageQuery.builder()
                .city(warehouseBySkuCityQueryReq.getCity())
                .skuList(warehouseBySkuCityQueryReq.getSkuList())
                .build());
        if(CollectionUtils.isEmpty(warehouseStorageDTOS)){
            return DubboResponse.getOK();
        }

        List<WarehouseSkuResp> warehouseSkuResps = new ArrayList<>();
        Map<String, List<WarehouseStorageDTO>> skuWarehouseDTOListMap = warehouseStorageDTOS.stream().collect(Collectors.groupingBy(WarehouseStorageDTO::getSku));
        for (String sku : skuWarehouseDTOListMap.keySet()) {
            List<WarehouseStorageDTO> warehouseStorageDTOList = skuWarehouseDTOListMap.get(sku);
            if(CollectionUtils.isEmpty(warehouseStorageDTOList)){
                WarehouseSkuResp warehouseSkuResp = new WarehouseSkuResp();
                warehouseSkuResp.setSku(sku);
                warehouseSkuResp.setWarehouseNos(new ArrayList<>());
                warehouseSkuResps.add(warehouseSkuResp);
                continue;
            }
            WarehouseSkuResp warehouseSkuResp = new WarehouseSkuResp();
            warehouseSkuResp.setSku(sku);
            warehouseSkuResp.setWarehouseNos(warehouseStorageDTOList.stream().map(WarehouseStorageDTO::getWarehouseNo).collect(Collectors.toList()));

            warehouseSkuResps.add(warehouseSkuResp);
        }
        return DubboResponse.getOK(warehouseSkuResps);
    }

    @Override
    public DubboResponse<List<WarehouseBaseInfoByNoResp>> queryBaseInfoByWarehouseNo(WarehouseBaseInfoByNoReq warehouseBaseInfoByNoReq) {
        if (warehouseBaseInfoByNoReq.getWarehouseNos().size() > 500) {
            return DubboResponse.getDefaultError("查询库存仓基础信息最多查询500条");
        }
        List<WarehouseBaseDTO> warehouseBaseDTOS = warehouseStorageCenterService.queryBaseInfoByNos(warehouseBaseInfoByNoReq.getWarehouseNos());
        return DubboResponse.getOK(warehouseSkuAreaDTOConverter.baseInfo2DubboResponse(warehouseBaseDTOS));
    }

    @Override
    public DubboResponse<List<WarehouseStorageDetailResp>> queryWarehouseList(WarehouseListQuery warehouseListQuery) {
        WncAssert.notNull(warehouseListQuery.getTenantId(),"tenantId不能为空");

        WarehouseStorageQuery warehouseStorageQuery = new WarehouseStorageQuery();
        warehouseStorageQuery.setTenantId(warehouseListQuery.getTenantId());
        warehouseStorageQuery.setWarehouseNos(warehouseListQuery.getWarehouseNos());
        warehouseStorageQuery.setType(warehouseListQuery.getType());
        warehouseStorageQuery.setStatus(warehouseListQuery.getStatus());
        warehouseStorageQuery.setWarehouseName(warehouseListQuery.getWarehouseName());

        return DubboResponse.getOK(warehouseStorageCenterService.queryWarehouseListByInfo(warehouseStorageQuery).stream().map(WarehouseStorageDTOConverter::warehouseStorageDTO2DetailResp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<List<WarehouseStorageCenterResp>> queryXmWarehouseList(XmWarehouseQueryReq xmWarehouseQueryReq) {
        WarehouseStorageQuery warehouseStorageQuery = new WarehouseStorageQuery();

        warehouseStorageQuery.setTenantId(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode().longValue());
        warehouseStorageQuery.setWarehouseNo(xmWarehouseQueryReq.getWarehouseNo());
        warehouseStorageQuery.setType(xmWarehouseQueryReq.getType());
        warehouseStorageQuery.setStatus(xmWarehouseQueryReq.getStatus());
        warehouseStorageQuery.setWarehouseName(xmWarehouseQueryReq.getWarehouseName());
        warehouseStorageQuery.setWarehouseNos(xmWarehouseQueryReq.getWarehouseNos());
        warehouseStorageQuery.setSelfWarehouseFlag(xmWarehouseQueryReq.getSelfWarehouseFlag());

        List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageCenterService.queryXmWarehouseList(warehouseStorageQuery);
        return DubboResponse.getOK(warehouseStorageDTOS.stream().map(WarehouseStorageDTOConverter::dto2WarehouseStorageCenterResp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<PageInfo<WarehouseStorageCenterBaseInfoResp>> queryWarehouseBaseInfoList(WarehouseQueryReq warehouseQueryReq) {
        WncAssert.notNull(warehouseQueryReq.getPageIndex(),"pageIndex不能为空");
        WncAssert.notNull(warehouseQueryReq.getPageSize(),"pageSize不能为空");

        WarehouseStorageQuery warehouseStorageQuery = new WarehouseStorageQuery();
        warehouseStorageQuery.setWarehouseNo(warehouseQueryReq.getWarehouseNo());
        warehouseStorageQuery.setWarehouseNos(warehouseQueryReq.getWarehouseNos());
        warehouseStorageQuery.setType(warehouseQueryReq.getType());
        warehouseStorageQuery.setStatus(warehouseQueryReq.getStatus());
        warehouseStorageQuery.setWarehouseName(warehouseQueryReq.getWarehouseName());
        warehouseStorageQuery.setSelfWarehouseFlag(warehouseQueryReq.getSelfWarehouseFlag());
        warehouseStorageQuery.setPageIndex(warehouseQueryReq.getPageIndex());
        warehouseStorageQuery.setPageSize(warehouseQueryReq.getPageSize());

        return DubboResponse.getOK(WarehouseStorageDTOConverter.dto2BasePage(warehouseStorageCenterService.queryBasePage(warehouseStorageQuery)));
    }

    @Override
    public DubboResponse<List<WarehouseStorageCenterResp>> queryAllPopWarehouseList() {
        List<Integer> popWarehouseNos = wncConfig.queryPopWarehouseNos();
        if (CollectionUtils.isEmpty(popWarehouseNos)) {
            return DubboResponse.getOK();
        }

        WarehouseStorageQuery warehouseStorageQuery = new WarehouseStorageQuery();
        warehouseStorageQuery.setTenantId(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode().longValue());
        warehouseStorageQuery.setWarehouseNos(popWarehouseNos);
        List<WarehouseStorageDTO> warehouseStorageDTOS = warehouseStorageCenterService.queryXmWarehouseList(warehouseStorageQuery);
        return DubboResponse.getOK(warehouseStorageDTOS.stream().map(WarehouseStorageDTOConverter::dto2WarehouseStorageCenterResp).collect(Collectors.toList()));
    }

}
